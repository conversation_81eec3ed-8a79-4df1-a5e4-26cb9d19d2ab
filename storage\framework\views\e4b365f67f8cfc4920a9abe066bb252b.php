<?php if($paginator->hasPages()): ?>
    <?php
        $isLastPage = !$paginator->hasMorePages();
        $isFirstPage = $paginator->onFirstPage();
        $totalPages = $paginator->lastPage();
    ?>



    
    <div class="w-full text-center">
        
    <?php if($totalPages > 1): ?>
        <div class="w-full text-center pt20">
            <?php if(!$isLastPage): ?>
                <a href="<?php echo e($paginator->nextPageUrl()); ?>"
                   style=" background-color: #181a20; color: #ffffff; font-weight: 700; font-size: 15px; line-height: 20px; padding: 12px 24px; border: none; border-radius: 999px; cursor: pointer; user-select: none; ">
                    <?php echo e(__('Next')); ?>

                </a><?php endif; ?>
        </div>
    <?php endif; ?>


    
        <div class="w-full text-center pt20 pb50">
        <ul class="flat-pagination properties-pagination inline-flex justify-center gap-2">
            
            <?php if(! $paginator->onFirstPage()): ?>
                <li>
                    <a href="<?php echo e($paginator->previousPageUrl()); ?>" class="page-numbers" aria-label="<?php echo e(trans('pagination.previous')); ?>">
                        <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-chevron-left'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                    </a>
                </li>
            <?php endif; ?>

            
            <?php
                $lastPage = $paginator->lastPage();
                $currentPage = $paginator->currentPage();
            ?>

            <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(is_string($element)): ?>
                    <?php continue; ?>
                <?php endif; ?>

                <?php if(is_array($element)): ?>
                    <?php $dotShown = false; ?>

                    <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $showPage = (
                                $page <= 4 ||
                                $page > $lastPage - 2 ||
                                abs($page - $currentPage) <= 1
                            );
                        ?>

                        <?php if($showPage): ?>
                            <li>
                                <a href="<?php echo e($url); ?>" class="<?php echo \Illuminate\Support\Arr::toCssClasses(['page-numbers', 'current' => $page == $currentPage]); ?>"><?php echo e($page); ?></a>
                            </li>
                            <?php $dotShown = false; ?>
                        <?php elseif(!$dotShown): ?>
                            <li><span class="page-numbers">...</span></li>
                            <?php $dotShown = true; ?>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            
            <?php if($paginator->hasMorePages()): ?>
                <li>
                    <a href="<?php echo e($paginator->nextPageUrl()); ?>" class="page-numbers" aria-label="<?php echo e(trans('pagination.next')); ?>">
                        <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-chevron-right'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                    </a>
                </li>
            <?php endif; ?>
        </ul>
    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/partials/pagination.blade.php ENDPATH**/ ?>