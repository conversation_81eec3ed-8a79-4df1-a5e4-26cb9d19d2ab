<?php

namespace Xmetr\SocialLogin\Http\Controllers;

use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Base\Http\Controllers\BaseController;
use Xmetr\Base\Http\Responses\BaseHttpResponse;
use Xmetr\Media\Facades\RvMedia;
use Xmetr\SocialLogin\Facades\SocialService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Laravel\Socialite\AbstractUser;
use Laravel\Socialite\Facades\Socialite;
use Laravel\Socialite\Two\InvalidStateException;

class SocialLoginController extends BaseController
{
    public function redirectToProvider(string $provider, Request $request)
    {
        $this->ensureProviderIsExisted($provider);

        $guard = $this->guard($request);

        if (! $guard) {
            return $this
                ->httpResponse()
                ->setError()
                ->setNextUrl(BaseHelper::getHomepageUrl());
        }

        if (BaseHelper::hasDemoModeEnabled() && $provider !== 'google') {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('This feature is temporary disabled in demo mode. Please use another login option. Such as Google.'));
        }

        $this->setProvider($provider);

        // Store session data with additional context for state validation
        session([
            'social_login_guard_current' => $guard,
            'social_login_provider_current' => $provider,
            'social_login_request_time' => time(),
            'social_login_request_url' => $request->fullUrl(),
        ]);

        // Store intended URL for redirect after social login
        $this->storeIntendedUrl($request);

        // Force session save to ensure data is persisted
        if (config('plugins.social-login.general.oauth.force_session_save', true)) {
            session()->save();
        }

        return Socialite::driver($provider)->redirect();
    }

    protected function guard(?Request $request = null)
    {
        if ($request) {
            $guard = $request->input('guard');
        } else {
            $guard = session('social_login_guard_current');
        }

        if (! $guard) {
            $guard = array_key_first(SocialService::supportedModules());
        }

        if (! $guard || ! SocialService::isSupportedModuleByKey($guard) || Auth::guard($guard)->check()) {
            return false;
        }

        return $guard;
    }

    protected function setProvider(string $provider): bool
    {
        config()->set([
            'services.' . $provider => [
                'client_id' => SocialService::setting($provider . '_app_id'),
                'client_secret' => SocialService::setting($provider . '_app_secret'),
                'redirect' => route('auth.social.callback', $provider),
            ],
        ]);

        return true;
    }

    public function handleProviderCallback(string $provider)
    {
        $this->ensureProviderIsExisted($provider);

        // Validate session state before proceeding
        $sessionValidation = $this->validateSessionState($provider);
        if ($sessionValidation !== true) {
            return $sessionValidation;
        }

        $guard = $this->guard();

        if (! $guard) {
            return $this
                ->httpResponse()
                ->setError()
                ->setNextUrl(BaseHelper::getHomepageUrl())
                ->setMessage(__('An error occurred while trying to login'));
        }

        $this->setProvider($provider);

        $providerData = Arr::get(SocialService::supportedModules(), $guard);

        try {
            /**
             * @var AbstractUser $oAuth
             */
            $oAuth = Socialite::driver($provider)->user();
        } catch (Exception $exception) {
            $message = $exception->getMessage();

            if (in_array($provider, ['github', 'facebook'])) {
                $message = json_encode($message);
            }

            if (! $message) {
                $message = __('An error occurred while trying to login');
            }

            if ($exception instanceof InvalidStateException) {
                // Clear potentially corrupted session data
                $this->clearSocialLoginSession();

                $message = __('Authentication session expired. Please try logging in again.');

                // Log the error for debugging
                BaseHelper::logError($exception, [
                    'provider' => $provider,
                    'guard' => $guard,
                    'session_data' => session()->all(),
                    'request_url' => request()->fullUrl(),
                ]);
            }

            return $this
                ->httpResponse()
                ->setError()
                ->setNextUrl($providerData['login_url'])
                ->setMessage($message);
        }

        if (! $oAuth->getEmail()) {
            return $this
                ->httpResponse()
                ->setError()
                ->setNextUrl($providerData['login_url'])
                ->setMessage(__('Cannot login, no email provided!'));
        }

        $model = new $providerData['model']();

        $account = $model->where('email', $oAuth->getEmail())->first();

        if (! $account) {
            $beforeProcessData = apply_filters('social_login_before_creating_account', null, $oAuth, $providerData);

            if ($beforeProcessData instanceof BaseHttpResponse) {
                return $beforeProcessData;
            }

            $avatarId = null;

            try {
                $url = $oAuth->getAvatar();
                if ($url) {
                    $result = RvMedia::uploadFromUrl($url, 0, $model->upload_folder ?: 'accounts', 'image/png');
                    if (! $result['error']) {
                        $avatarId = $result['data']->id;
                    }
                }
            } catch (Exception $exception) {
                BaseHelper::logError($exception);
            }

            $data = [
                'name' => $oAuth->getName() ?: $oAuth->getEmail(),
                'email' => $oAuth->getEmail(),
                'password' => Hash::make(Str::random(36)),
                'avatar_id' => $avatarId,
            ];

            $data = apply_filters('social_login_before_saving_account', $data, $oAuth, $providerData);

            $account = $model;
            $account->fill($data);
            $account->confirmed_at = Carbon::now();
            $account->save();
        }

        Auth::guard($guard)->login($account, true);

        $redirectUrl = $providerData['redirect_url'] ?: BaseHelper::getHomepageUrl();

        $redirectUrl = session()->has('url.intended') ? session('url.intended') : $redirectUrl;

        // Clear the intended URL after use to prevent reuse
        session()->forget('url.intended');

        // Clear social login session data after successful authentication
        $this->clearSocialLoginSession();

        return $this
            ->httpResponse()
            ->setNextUrl($redirectUrl)
            ->setMessage(trans('core/acl::auth.login.success'));
    }

    protected function ensureProviderIsExisted(string $provider): void
    {
        abort_if(! in_array($provider, SocialService::getProviderKeys(), true), 404);
    }

    /**
     * Validate session state for OAuth callback
     */
    protected function validateSessionState(string $provider)
    {
        $sessionProvider = session('social_login_provider_current');
        $sessionTime = session('social_login_request_time');
        $sessionGuard = session('social_login_guard_current');

        // Check if session data exists
        if (!$sessionProvider || !$sessionTime || !$sessionGuard) {
            return $this
                ->httpResponse()
                ->setError()
                ->setNextUrl(route('public.account.login'))
                ->setMessage(__('Authentication session not found. Please try logging in again.'));
        }

        // Check if provider matches
        if ($sessionProvider !== $provider) {
            return $this
                ->httpResponse()
                ->setError()
                ->setNextUrl(route('public.account.login'))
                ->setMessage(__('Authentication provider mismatch. Please try logging in again.'));
        }

        // Check if session is not too old (configurable timeout)
        $timeout = config('plugins.social-login.general.oauth.session_timeout', 1800);
        if (time() - $sessionTime > $timeout) {
            $this->clearSocialLoginSession();
            return $this
                ->httpResponse()
                ->setError()
                ->setNextUrl(route('public.account.login'))
                ->setMessage(__('Authentication session expired. Please try logging in again.'));
        }

        return true;
    }

    /**
     * Clear social login session data
     */
    protected function clearSocialLoginSession(): void
    {
        session()->forget([
            'social_login_guard_current',
            'social_login_provider_current',
            'social_login_request_time',
            'social_login_request_url',
        ]);
    }

    /**
     * Store intended URL for redirect after social login
     */
    protected function storeIntendedUrl(Request $request): void
    {
        // Priority 1: Check for intended_url parameter (from social login links)
        if ($request->has('intended_url') && $request->input('intended_url')) {
            $intendedUrl = $request->input('intended_url');

            // Validate and store the intended URL
            if ($this->isValidIntendedUrl($intendedUrl)) {
                session(['url.intended' => $intendedUrl]);

                // Log for debugging single property page issues
                if (config('plugins.social-login.general.oauth.log_oauth_errors', true)) {
                    BaseHelper::logInfo('Social login intended URL stored from parameter', [
                        'intended_url' => $intendedUrl,
                        'provider' => $request->route('provider'),
                        'referer' => $request->header('referer'),
                    ]);
                }
                return;
            }
        }

        // Priority 2: Check for existing intended URL in session (from modal context)
        $existingIntended = session('url.intended');
        if ($existingIntended && $this->isValidIntendedUrl($existingIntended)) {
            // Keep the existing intended URL - don't override it
            if (config('plugins.social-login.general.oauth.log_oauth_errors', true)) {
                BaseHelper::logInfo('Social login keeping existing intended URL', [
                    'existing_intended' => $existingIntended,
                    'provider' => $request->route('provider'),
                ]);
            }
            return;
        }

        // Priority 3: Check for HTTP referer (when user clicks social login from a page)
        $referer = $request->header('referer');
        if ($referer && $this->isValidIntendedUrl($referer)) {
            session(['url.intended' => $referer]);

            if (config('plugins.social-login.general.oauth.log_oauth_errors', true)) {
                BaseHelper::logInfo('Social login intended URL stored from referer', [
                    'referer' => $referer,
                    'provider' => $request->route('provider'),
                ]);
            }
            return;
        }

        // Priority 4: For single property pages and other dynamic routes,
        // try to reconstruct the URL from the current request
        $currentUrl = $this->reconstructIntendedUrl($request);
        if ($currentUrl && $this->isValidIntendedUrl($currentUrl)) {
            session(['url.intended' => $currentUrl]);

            if (config('plugins.social-login.general.oauth.log_oauth_errors', true)) {
                BaseHelper::logInfo('Social login intended URL reconstructed', [
                    'reconstructed_url' => $currentUrl,
                    'provider' => $request->route('provider'),
                ]);
            }
            return;
        }

        // If no valid intended URL is found, don't store anything
        // The callback will use the default redirect URL
        if (config('plugins.social-login.general.oauth.log_oauth_errors', true)) {
            BaseHelper::logWarning('Social login no valid intended URL found', [
                'provider' => $request->route('provider'),
                'referer' => $request->header('referer'),
                'current_url' => $request->fullUrl(),
            ]);
        }
    }

    /**
     * Check if a URL is valid for use as an intended redirect URL
     */
    protected function isValidIntendedUrl(string $url): bool
    {
        // Must be a valid URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        // Must be from the same domain
        $urlHost = parse_url($url, PHP_URL_HOST);
        $appHost = parse_url(config('app.url'), PHP_URL_HOST);
        if ($urlHost !== $appHost) {
            return false;
        }

        // Exclude auth-related pages
        $excludedPaths = ['/login', '/register', '/auth/', '/logout', '/password/', '/admin'];
        foreach ($excludedPaths as $path) {
            if (str_contains($url, $path)) {
                return false;
            }
        }

        // Additional validation for single property pages and other dynamic routes
        // These should always be valid intended URLs
        $validPatterns = [
            '/properties/', // Property listing and single property pages
            '/projects/',   // Project listing and single project pages
            '/search',      // Search results pages
            '/',            // Homepage and other static pages
        ];

        $isValidPattern = false;
        foreach ($validPatterns as $pattern) {
            if (str_contains($url, $pattern) || $url === rtrim(config('app.url'), '/')) {
                $isValidPattern = true;
                break;
            }
        }

        return $isValidPattern;
    }

    /**
     * Reconstruct intended URL for dynamic routes like single property pages
     */
    protected function reconstructIntendedUrl(Request $request): ?string
    {
        $referer = $request->header('referer');

        // If we have a valid referer, use it
        if ($referer && filter_var($referer, FILTER_VALIDATE_URL)) {
            return $referer;
        }

        // Try to get the previous URL from session
        $previousUrl = session()->previousUrl();
        if ($previousUrl && $this->isValidIntendedUrl($previousUrl)) {
            return $previousUrl;
        }

        // Fallback to homepage
        return route('public.index');
    }
}
